import logging
import os
from dotenv import load_dotenv
from contextlib import asynccontextmanager

import tiktoken
from fastapi import FastAPI
from langchain_openai import AzureChatOpenAI

from app.constants import AZURE_OPENAI_SERVICE_URL, AZURE_OPENAI_API_KEY, SECRET_DIR_PATH, \
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_API_VERSION, TIKTOKEN_MODEL_NAME
from app.routes.service import router
from app.utils import get_secret
from fastapi.middleware.cors import CORSMiddleware

load_dotenv()

# Load openai service URL and key from environment variables
azure_openai_url = os.environ.get(AZURE_OPENAI_SERVICE_URL)
azure_openai_key = get_secret(SECRET_DIR_PATH, AZURE_OPENAI_API_KEY) # os.environ.get(AZURE_OPENAI_API_KEY)

# Create a logger instance
logger = logging.getLogger(__name__)

# Configure the logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s -  %(message)s')

@asynccontextmanager
async def lifespan(app: FastAPI):
    llm = None
    encoding_model = TIKTOKEN_MODEL_NAME

    try:
        # Initialize the language model
        logger.info("Initializing the AZURE OpenAI model.")

        llm = AzureChatOpenAI(
            azure_endpoint=azure_openai_url,
            api_key=azure_openai_key,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            api_version=AZURE_OPENAI_API_VERSION,
            max_retries=0
        )

        try:
            _ = await llm.ainvoke("Hi, I'm Copilot.")
        except Exception:
            logger.warning(f"Failed to connect to model '{AZURE_OPENAI_DEPLOYMENT_NAME}'. Retry with 'gpt-4o' model.")
            llm = AzureChatOpenAI(
                azure_endpoint=azure_openai_url,
                api_key=azure_openai_key,
                deployment_name="gpt-4o-2024-11-20",
                api_version=AZURE_OPENAI_API_VERSION,
                max_retries=0)
            encoding_model = "gpt-4o"
        encoder = tiktoken.encoding_for_model(encoding_model)

    finally:
        if not llm:
            # When model initialization fails
            # Raise an exception to exit the application
            raise Exception("Failed to initialize the Azure OpenAI model")
        encoder = tiktoken.encoding_for_model(encoding_model)
        app.state.llm = llm
        app.state.encoder = encoder

    yield


# Create an instance of the FastAPI application
app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development only - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the router from the service module
app.include_router(router)
