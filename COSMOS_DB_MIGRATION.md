# Cosmos DB Configuration Migration

## Overview

The Cosmos DB configuration has been updated to use a connection URL format for consistency with other AI implementations in the organization. This change maintains backward compatibility with the existing endpoint/key configuration.

## New Configuration Format (Recommended)

### Environment Variable
```bash
COSMOS_DB_CONNECTION_URL="AccountEndpoint=https://your-account.documents.azure.com:443/;AccountKey=your-key;"
```

### Required Additional Variables
```bash
COSMOS_DB_DATABASE="your-database-name"
COSMOS_DB_CONTAINER="your-container-name"
```

## Legacy Configuration Format (Deprecated)

The following configuration is still supported but deprecated:

```bash
COSMOS_DB_ENDPOINT="https://your-account.documents.azure.com:443/"
COSMOS_DB_KEY="your-key"
COSMOS_DB_DATABASE="your-database-name"
COSMOS_DB_CONTAINER="your-container-name"
```

## Migration Steps

1. **Update your environment variables** to use the new `COSMOS_DB_CONNECTION_URL` format
2. **Remove the old variables** `COSMOS_DB_ENDPOINT` and `COSMOS_DB_KEY` once migration is complete
3. **Test the application** to ensure Cosmos DB connectivity works correctly

## Benefits

- **Consistency**: Aligns with other AI implementations in the organization
- **Simplicity**: Single connection string instead of separate endpoint and key
- **Security**: Connection string can be managed as a single secret
- **Backward Compatibility**: Existing configurations continue to work during transition

## Implementation Details

The application will:
1. First try to use `COSMOS_DB_CONNECTION_URL` if provided
2. Fall back to the legacy `COSMOS_DB_ENDPOINT` and `COSMOS_DB_KEY` if connection URL is not available
3. Log a warning when using the deprecated configuration
4. Provide clear error messages if configuration is incomplete

## Example Connection URL Format

```
AccountEndpoint=https://your-cosmos-account.documents.azure.com:443/;AccountKey=your-primary-or-secondary-key;
```

Replace:
- `your-cosmos-account` with your actual Cosmos DB account name
- `your-primary-or-secondary-key` with your actual primary or secondary key from the Azure portal
