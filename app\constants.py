# secret constants
SECRET_DIR_PATH = "/mnt/csi/secret-ai-copilot"

# chat history constants
CONVERSATION_CACHE_TTL = 3600 # 3600 seconds = 1 hour
COPILOT_NAMESPACE = "copilot"
MAX_NUM_HISTORY_RECORDS = 10
CONN_TIMEOUT = 2000

# cosmos db constants
# Connection URL format: AccountEndpoint=https://your-account.documents.azure.com:443/;AccountKey=your-key;
COSMOS_DB_CONNECTION_URL = "COSMOS_DB_CONNECTION_URL"
COSMOS_DB_KEY = "COSMOS_DB_KEY"  # Deprecated - use COSMOS_DB_CONNECTION_URL
COSMOS_DB_ENDPOINT = "COSMOS_DB_ENDPOINT"  # Deprecated - use COSMOS_DB_CONNECTION_URL
COSMOS_DB_DATABASE = "COSMOS_DB_DATABASE"
COSMOS_DB_CONTAINER = "COSMOS_DB_CONTAINER"

# azure constants
AZURE_OPENAI_API_KEY = "AZURE_OPENAI_API_KEY"
AZURE_OPENAI_SERVICE_URL = "AZURE_OPENAI_SERVICE_URL"
AZURE_OPENAI_API_VERSION = "2025-01-01-preview"
AZURE_OPENAI_DEPLOYMENT_NAME = "gpt-4o-2024-11-20"

# tiktoken constants
TIKTOKEN_MODEL_NAME = "gpt-4o"

# opaque token authentication constants
ASGARDEO_INTROSPECTION_ENDPOINT = "ASGARDEO_INTROSPECTION_ENDPOINT"
ASGARDEO_INTROSPECTION_CLIENT_ID = "ASGARDEO_INTROSPECTION_CLIENT_ID"
ASGARDEO_INTROSPECTION_CLIENT_SECRET = "ASGARDEO_INTROSPECTION_CLIENT_SECRET"

# service constants
X_REQUEST_ID = "x-request-id"
CORRELATION_ID = "correlation-id"

# assistant enabling flags
DOCS_ASSISTANT_ENABLED = "DOCS_ASSISTANT_ENABLED"

# retry manager
MAX_RETRY_ATTEMPTS = 2
